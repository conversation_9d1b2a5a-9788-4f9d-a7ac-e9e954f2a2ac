// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get settings => 'Settings';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get soundClick => 'Click Sounds';

  @override
  String get soundScroll => 'Scroll Sounds';

  @override
  String get language => 'Language';

  @override
  String get arabic => 'Arabic';

  @override
  String get english => 'English';

  @override
  String get skip => 'Skip';

  @override
  String get next => 'Next';

  @override
  String get start => 'Start';

  @override
  String get searchHint => 'Search your favorite destination...';

  @override
  String get filter => 'Filter';

  @override
  String get loading => 'Loading...';

  @override
  String get noResults => 'No results found';

  @override
  String get perNight => 'SR / night';

  @override
  String get wifi => 'Wi-Fi';

  @override
  String get addToFavorites => 'Add to favorites';

  @override
  String get appName => 'Gather Point';

  @override
  String get selectCity => 'Select City';

  @override
  String get detectingLocation => 'Detecting location...';

  @override
  String get locationPermissionError =>
      'Please enable location permission to use the app';

  @override
  String get browseReels => 'Browse Reels';

  @override
  String get discoverLatestVisualContent => 'Discover Latest Visual Content';

  @override
  String get exploreCategories => 'Explore Categories';

  @override
  String get nearbyPlaces => 'Nearby Places';

  @override
  String get popularDestinations => 'Popular Destinations';

  @override
  String get featuredPlaces => 'Featured Places';

  @override
  String get discoverMore => 'Discover More';

  @override
  String get viewAll => 'View All';

  @override
  String get bookNow => 'Book Now';

  @override
  String get pricePerNight => 'Price per Night (SAR)';

  @override
  String get guests => 'Guests';

  @override
  String get rooms => 'Rooms';

  @override
  String get bathrooms => 'Bathrooms';

  @override
  String get amenities => 'Amenities';

  @override
  String get location => 'Location';

  @override
  String get reviews => 'Reviews';

  @override
  String get rating => 'Rating';

  @override
  String get excellent => 'Excellent';

  @override
  String get veryGood => 'Very Good';

  @override
  String get good => 'Good';

  @override
  String get fair => 'Fair';

  @override
  String get poor => 'Poor';

  @override
  String get soundSettings => 'Sound Settings';

  @override
  String get customizeExperience => 'Customize your app experience';

  @override
  String get themeEnabled => 'Dark theme enabled';

  @override
  String get themeDisabled => 'Light theme enabled';

  @override
  String get soundClickEnabled => 'Click sounds enabled';

  @override
  String get soundClickDisabled => 'Click sounds disabled';

  @override
  String get soundScrollEnabled => 'Scroll sounds enabled';

  @override
  String get soundScrollDisabled => 'Scroll sounds disabled';

  @override
  String get createProperty => 'Create Property';

  @override
  String get propertyTitle => 'Property Title';

  @override
  String get propertyDescription => 'Description';

  @override
  String get selectCategory => 'Select Category';

  @override
  String get titleAndDescription => 'Title & Description';

  @override
  String get pickLocation => 'Pick Location';

  @override
  String get imageGallery => 'Image Gallery';

  @override
  String get addImage => 'Add Image';

  @override
  String get availableServices => 'Available Services';

  @override
  String get selectServices => 'Select available services';

  @override
  String get pricing => 'Pricing';

  @override
  String get dailyPrice => 'Daily Price';

  @override
  String get weeklyPrice => 'Weekly Price';

  @override
  String get monthlyPrice => 'Monthly Price';

  @override
  String get commission => 'Commission';

  @override
  String get bookingDetails => 'Booking Details & Policies';

  @override
  String get numberOfBathrooms => 'Number of Bathrooms';

  @override
  String get numberOfBedrooms => 'Number of Bedrooms';

  @override
  String get numberOfGuests => 'Number of Guests';

  @override
  String get bookingPolicy => 'Booking Policy';

  @override
  String get cancellationPolicy => 'Cancellation Policy';

  @override
  String get back => 'Back';

  @override
  String get submit => 'Submit';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirmSubmission => 'Confirm Submission';

  @override
  String get confirmSubmissionMessage =>
      'Are you sure you want to submit this property?';

  @override
  String get error => 'Error';

  @override
  String get ok => 'OK';

  @override
  String get propertyCreatedSuccessfully => 'Property created successfully';

  @override
  String get tapToUploadImages => 'Tap to upload images';

  @override
  String get latitude => 'Latitude';

  @override
  String get longitude => 'Longitude';

  @override
  String get enterPrice => 'Enter price';

  @override
  String get failedToLoadCategories => 'Failed to load categories';

  @override
  String get failedToLoadFacilities => 'Failed to load facilities';

  @override
  String get failedToCreateItem => 'Failed to create item';

  @override
  String get home => 'Home';

  @override
  String get search => 'Search';

  @override
  String get reels => 'Reels';

  @override
  String get profile => 'Profile';

  @override
  String get myBookings => 'My Bookings';

  @override
  String get myListings => 'My Listings';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get personalInfo => 'Personal Information';

  @override
  String get contactInfo => 'Contact Information';

  @override
  String get additionalInfo => 'Additional Information';

  @override
  String get fullName => 'Full Name';

  @override
  String get bio => 'Bio';

  @override
  String get email => 'Email';

  @override
  String get phone => 'Phone';

  @override
  String get gender => 'Gender';

  @override
  String get birthdate => 'Birthdate';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get notSpecified => 'Not Specified';

  @override
  String get selectBirthdate => 'Select Birthdate';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get profileImage => 'Profile Image';

  @override
  String get tapToChangeImage => 'Tap to change image';

  @override
  String get totalBookings => 'Total Bookings';

  @override
  String get confirmedBookings => 'Confirmed Bookings';

  @override
  String get totalProperties => 'Total Properties';

  @override
  String get totalViews => 'Total Views';

  @override
  String get totalReservations => 'Total Reservations';

  @override
  String get all => 'All';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get pending => 'Pending';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get underReview => 'Under Review';

  @override
  String get checkIn => 'Check In';

  @override
  String get checkOut => 'Check Out';

  @override
  String get totalPrice => 'Total Price';

  @override
  String get viewDetails => 'View Details';

  @override
  String get cancelBooking => 'Cancel Booking';

  @override
  String get rebookProperty => 'Rebook Property';

  @override
  String get editProperty => 'Edit Property';

  @override
  String get bedrooms => 'Bedrooms';

  @override
  String get propertyDetails => 'Property Details';

  @override
  String get hostMode => 'Host Mode';

  @override
  String get enableHostMode => 'Enable host mode to manage your properties';

  @override
  String get logout => 'Logout';

  @override
  String get appearance => 'Appearance';

  @override
  String get notifications => 'Notifications';

  @override
  String get privacy => 'Privacy & Security';

  @override
  String get about => 'About App';

  @override
  String get manageNotifications => 'Manage notification settings';

  @override
  String get privacySettings => 'Privacy and security settings';

  @override
  String get appInfo => 'App information and version';

  @override
  String get version => 'Version';

  @override
  String get appDescription => 'Property booking and rental app';

  @override
  String get noBookingsYet => 'No bookings yet';

  @override
  String get noBookingsSubtitle => 'You haven\'t made any bookings yet';

  @override
  String get exploreProperties => 'Explore Properties';

  @override
  String get noPropertiesYet => 'No properties yet';

  @override
  String get noPropertiesSubtitle => 'Start by adding your first property';

  @override
  String get addProperty => 'Add Property';

  @override
  String get searching => 'Searching...';

  @override
  String get tryDifferentKeywords => 'Try searching with different keywords';

  @override
  String get backToSearch => 'Back to Search';

  @override
  String get loadingReels => 'Loading reels...';

  @override
  String get failedToLoadReels => 'Failed to load reels';

  @override
  String get checkConnection => 'Check your internet connection';

  @override
  String get retry => 'Retry';

  @override
  String get shareProperty => 'Share Property';

  @override
  String get aboutThisPlace => 'About this place';

  @override
  String get whatThisPlaceOffers => 'What this place offers';

  @override
  String get whereYoullBe => 'Where you\'ll be';

  @override
  String get hostedBy => 'Hosted by';

  @override
  String get superhost => 'Superhost';

  @override
  String get yearsHosting => 'years hosting';

  @override
  String get since => 'Since';

  @override
  String get change => 'Change';

  @override
  String get showMore => 'Show more';

  @override
  String get showLess => 'Show less';

  @override
  String get noAmenitiesListed => 'No amenities listed';

  @override
  String get showAllAmenities => 'Show all amenities';

  @override
  String get mapView => 'Map view';

  @override
  String get guestReview => 'Guest Review';

  @override
  String get reserve => 'Reserve';

  @override
  String get bookingFee => 'Booking fee';

  @override
  String get serviceFee => 'Service fee';

  @override
  String get taxes => 'Taxes';

  @override
  String get total => 'Total';

  @override
  String get currentLocation => 'Current Location';

  @override
  String get searchPlaceholder => 'Welcome... search for what you want';

  @override
  String get categories => 'Categories';

  @override
  String get popularPlaces => 'Popular Places';

  @override
  String get seeAll => 'See All';

  @override
  String get views => 'Views';

  @override
  String get properties => 'Properties';

  @override
  String get reservations => 'Reservations';

  @override
  String get status => 'Status';

  @override
  String get price => 'Price';

  @override
  String get checkInDate => 'Check-in Date';

  @override
  String get checkOutDate => 'Check-out Date';

  @override
  String get propertyName => 'Property Name';

  @override
  String get propertyLocation => 'Property Location';

  @override
  String get totalAmount => 'Total Amount';

  @override
  String get bookingStatus => 'Booking Status';

  @override
  String get bookingDate => 'Booking Date';

  @override
  String get hostName => 'Host Name';

  @override
  String get contactHost => 'Contact Host';

  @override
  String get cancelReservation => 'Cancel Reservation';

  @override
  String get modifyReservation => 'Modify Reservation';

  @override
  String get leaveReview => 'Leave Review';

  @override
  String get downloadReceipt => 'Download Receipt';

  @override
  String get propertyType => 'Property Type';

  @override
  String get description => 'Description';

  @override
  String get rules => 'Rules';

  @override
  String get safetyFeatures => 'Safety Features';

  @override
  String get accessibility => 'Accessibility';

  @override
  String get nearbyAttractions => 'Nearby Attractions';

  @override
  String get transportation => 'Transportation';

  @override
  String get checkInInstructions => 'Check-in Instructions';

  @override
  String get houseRules => 'House Rules';

  @override
  String get importantInfo => 'Important Information';

  @override
  String get instantBook => 'Instant Book';

  @override
  String get verified => 'Verified';

  @override
  String get newListing => 'New Listing';

  @override
  String get rareFind => 'Rare Find';

  @override
  String get guestFavorite => 'Guest Favorite';

  @override
  String get topRated => 'Top Rated';

  @override
  String get luxuryStay => 'Luxury Stay';

  @override
  String get budgetFriendly => 'Budget Friendly';

  @override
  String get familyFriendly => 'Family Friendly';

  @override
  String get petFriendly => 'Pet Friendly';

  @override
  String get workFriendly => 'Work Friendly';

  @override
  String get partyFriendly => 'Party Friendly';

  @override
  String get smokingAllowed => 'Smoking Allowed';

  @override
  String get noSmoking => 'No Smoking';

  @override
  String get freeWifi => 'Free WiFi';

  @override
  String get freeParking => 'Free Parking';

  @override
  String get pool => 'Pool';

  @override
  String get gym => 'Gym';

  @override
  String get spa => 'Spa';

  @override
  String get restaurant => 'Restaurant';

  @override
  String get bar => 'Bar';

  @override
  String get laundry => 'Laundry';

  @override
  String get kitchen => 'Kitchen';

  @override
  String get airConditioning => 'Air Conditioning';

  @override
  String get heating => 'Heating';

  @override
  String get tv => 'TV';

  @override
  String get workspace => 'Workspace';

  @override
  String get balcony => 'Balcony';

  @override
  String get garden => 'Garden';

  @override
  String get beachAccess => 'Beach Access';

  @override
  String get mountainView => 'Mountain View';

  @override
  String get cityView => 'City View';

  @override
  String get oceanView => 'Ocean View';

  @override
  String get lakeView => 'Lake View';

  @override
  String get gardenView => 'Garden View';

  @override
  String get streetView => 'Street View';

  @override
  String get noView => 'No View';

  @override
  String get bookingSummary => 'Booking Summary';

  @override
  String get hostDashboard => 'Host Dashboard';

  @override
  String get walletBalance => 'Wallet Balance';

  @override
  String get totalEarnings => 'Total Earnings';

  @override
  String get recentBookings => 'Recent Bookings';

  @override
  String get recentReviews => 'Recent Reviews';

  @override
  String get withdraw => 'Withdraw';

  @override
  String get earnings => 'Earnings';

  @override
  String get bookingsChart => 'Bookings Chart';

  @override
  String get earningsChart => 'Earnings Chart';

  @override
  String get thisMonth => 'This Month';

  @override
  String get lastMonth => 'Last Month';

  @override
  String get thisYear => 'This Year';

  @override
  String get availableBalance => 'Available Balance';

  @override
  String get pendingEarnings => 'Pending Earnings';

  @override
  String get totalWithdrawn => 'Total Withdrawn';

  @override
  String get withdrawFunds => 'Withdraw Funds';

  @override
  String get enterAmount => 'Enter Amount';

  @override
  String get minimumWithdraw => 'Minimum withdrawal: SR 50';

  @override
  String get withdrawalMethod => 'Withdrawal Method';

  @override
  String get bankTransfer => 'Bank Transfer';

  @override
  String get paypal => 'PayPal';

  @override
  String get processing => 'Processing';

  @override
  String get noRecentBookings => 'No recent bookings';

  @override
  String get noRecentReviews => 'No recent reviews';

  @override
  String get average => 'Average';

  @override
  String get guestName => 'Guest Name';

  @override
  String get checkInOut => 'Check-in/out';

  @override
  String get nightsStayed => 'Nights Stayed';

  @override
  String get earningsOverview => 'Earnings Overview';

  @override
  String get bookingsOverview => 'Bookings Overview';

  @override
  String get last6Months => 'Last 6 Months';

  @override
  String get guestComment => 'Guest Comment';

  @override
  String get dates => 'Dates';

  @override
  String get nights => 'Nights';

  @override
  String get noData => 'No data available';

  @override
  String get enterSearchTerm => 'Enter search term...';

  @override
  String get noSearchResults => 'No results found';

  @override
  String get noTitle => 'No title';

  @override
  String get noDescription => 'No description';

  @override
  String get tryDifferentSearch => 'Try searching with different words';

  @override
  String get searchResults => 'Search Results';

  @override
  String get noBookingsMessage => 'No recent bookings';

  @override
  String get noReviewsMessage => 'No recent reviews';

  @override
  String get hostModeDescription =>
      'Enable host mode to manage your properties';

  @override
  String get logoutConfirmation => 'Are you sure you want to logout?';

  @override
  String get mustLogin => 'You must login';

  @override
  String get mustLoginDescription => 'Please login to view your profile';

  @override
  String get login => 'Login';

  @override
  String get confirmReservation => 'Confirm Reservation';

  @override
  String get unitDetails => 'Unit Details';

  @override
  String get unitName => 'Unit Name';

  @override
  String get numberOfDays => 'Number of Days';

  @override
  String get reservationFrom => 'Reservation From';

  @override
  String get reservationTo => 'Reservation To';

  @override
  String get priceDetails => 'Price Details';

  @override
  String get finalPrice => 'Final Price';

  @override
  String get priceBredown => 'Price Breakdown';

  @override
  String get priceType => 'Price Type';

  @override
  String get weekendPrice => 'Weekend Price';

  @override
  String get normalDays => 'Normal Days';

  @override
  String get weekendDays => 'Weekend Days';

  @override
  String get confirmBooking => 'Confirm Booking';

  @override
  String get reservationConfirmed => 'Reservation confirmed successfully!';

  @override
  String get reservationFailed => 'Failed to confirm reservation';

  @override
  String get invalidDate => 'Invalid date';

  @override
  String get notAvailable => 'Not available';

  @override
  String get days => 'Days';

  @override
  String get reviewsOverview => 'Reviews Overview';

  @override
  String get totalReviews => 'Total Reviews';

  @override
  String get allReviews => 'All';

  @override
  String get highRated => 'High Rated';

  @override
  String get noReviewsFound => 'No reviews found';

  @override
  String get noReviewsMatchFilter => 'No reviews match the selected filter';

  @override
  String get foundHelpful => 'people found this review helpful';

  @override
  String get selectReservationDate => 'Select Reservation Date';

  @override
  String get reviewReservation => 'Review Reservation';

  @override
  String get pleaseSelectBothDates => 'Please select both dates';

  @override
  String get selectedPeriodNotAvailable =>
      'The selected period is not available, please choose another period.';

  @override
  String get errorFetchingInfo =>
      'An error occurred while fetching information';

  @override
  String get failedToLoadVideo => 'Failed to load video';

  @override
  String get gatherPoint => 'Gather Point';

  @override
  String get muteVideo => 'Mute Video';

  @override
  String get unmuteVideo => 'Unmute Video';

  @override
  String get playVideo => 'Play Video';

  @override
  String get pauseVideo => 'Pause Video';

  @override
  String get removeFromFavorites => 'Remove from Favorites';

  @override
  String get comment => 'Comment';

  @override
  String get share => 'Share';

  @override
  String get comments => 'Comments';

  @override
  String get writeComment => 'Write a comment...';

  @override
  String get postComment => 'Post Comment';

  @override
  String get noComments => 'No comments yet';

  @override
  String get commentPosted => 'Comment posted successfully';

  @override
  String get commentFailed => 'Failed to post comment';

  @override
  String get deleteComment => 'Delete Comment';

  @override
  String get editComment => 'Edit Comment';

  @override
  String get replyToComment => 'Reply to Comment';

  @override
  String get showComments => 'Show Comments';

  @override
  String get hideComments => 'Hide Comments';

  @override
  String get searchReels => 'Search Reels';

  @override
  String get filterReels => 'Filter Reels';

  @override
  String get allCategories => 'All Categories';

  @override
  String get sortBy => 'Sort By';

  @override
  String get newest => 'Newest';

  @override
  String get oldest => 'Oldest';

  @override
  String get mostLiked => 'Most Liked';

  @override
  String get mostCommented => 'Most Commented';

  @override
  String get applyFilter => 'Apply Filter';

  @override
  String get clearFilter => 'Clear Filter';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get additionalSettings => 'Additional Settings';

  @override
  String get notificationSettings => 'Notification Settings';

  @override
  String get aboutApp => 'About App';

  @override
  String get filterResults => 'Filter Results';

  @override
  String get priceRange => 'Price Range';

  @override
  String get minimumRating => 'Minimum Rating';

  @override
  String get resetFilters => 'Reset';

  @override
  String get applyFilters => 'Apply';

  @override
  String get tryDifferentSearchCriteria => 'Try adjusting your search criteria';

  @override
  String get priceLowToHigh => 'Price: Low to High';

  @override
  String get priceHighToLow => 'Price: High to Low';

  @override
  String get ratingHighToLow => 'Rating: High to Low';

  @override
  String get ratingLowToHigh => 'Rating: Low to High';

  @override
  String get popular => 'Popular';

  @override
  String get privacyAndSecurity => 'Privacy and security settings';

  @override
  String get appInformation => 'App information and version';

  @override
  String get pushNotifications => 'Push Notifications';

  @override
  String get eventNotifications => 'Event Notifications';

  @override
  String get messageNotifications => 'Message Notifications';

  @override
  String get marketingNotifications => 'Marketing Notifications';

  @override
  String get enableAllNotifications => 'Enable or disable all notifications';

  @override
  String get newEventsAndUpdates =>
      'Notifications about new events and updates';

  @override
  String get newMessagesAndChats =>
      'Notifications for new messages and conversations';

  @override
  String get offersAndMarketing =>
      'Notifications about offers and marketing news';

  @override
  String get testNotification => 'Test Notification';

  @override
  String get sendTestNotification => 'Send Test Notification';

  @override
  String get notificationPermissionRequired =>
      'Notification Permission Required';

  @override
  String get enableNotificationsInSettings =>
      'Please enable notifications in device settings';

  @override
  String get openSettings => 'Open Settings';

  @override
  String get dataAndPrivacy => 'Data & Privacy';

  @override
  String get dataCollection => 'Data Collection';

  @override
  String get thirdPartySharing => 'Third Party Sharing';

  @override
  String get dataRetention => 'Data Retention';

  @override
  String get yourRights => 'Your Rights';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get dataCollectionDesc => 'How we collect and use your data';

  @override
  String get thirdPartySharingDesc =>
      'Information about data sharing with partners';

  @override
  String get dataRetentionDesc => 'How long we keep your data';

  @override
  String get yourRightsDesc => 'Your privacy rights and how to exercise them';

  @override
  String get contactUsDesc => 'Get in touch with our support team';

  @override
  String get deleteAccountDesc => 'Permanently delete your account and data';

  @override
  String get proceedWithGoogle => 'Continue with Google';

  @override
  String get proceedWithApple => 'Continue with Apple';

  @override
  String get or => 'OR';

  @override
  String get welcomeGuest => 'Welcome our guest... continue';

  @override
  String get proceedAsGuest => 'Continue as Guest';

  @override
  String get proceedWithPhone => 'Continue with your phone number';

  @override
  String get verifyPhoneNumber => 'Verify your phone number';

  @override
  String get enterVerificationCode => 'Enter verification code';

  @override
  String get verificationCodeSent =>
      'A 4-digit code has been sent to your phone';

  @override
  String get proceedLabel => 'Continue';

  @override
  String get viewAllBookings => 'View All Bookings';

  @override
  String get viewAllReviews => 'View All Reviews';

  @override
  String get enterPhoneNumber => 'Enter your phone number';

  @override
  String get phoneNumberHint => '5xxxxxxxx';

  @override
  String get pleaseEnterPhoneNumber => 'Please enter phone number';

  @override
  String get pleaseCheckPhoneNumber => 'Please check phone number';

  @override
  String get invalidPhoneNumber => 'Invalid phone number';

  @override
  String get phoneNumberRequired => 'Phone number is required';

  @override
  String get resendCode => 'Resend code';

  @override
  String get didntReceiveCode => 'Didn\'t receive the code?';

  @override
  String get verificationFailed => 'Verification failed';

  @override
  String get invalidCode => 'Invalid code';

  @override
  String get codeExpired => 'Code expired';

  @override
  String get continueButton => 'Continue';

  @override
  String get exploreAllCategoriesSubtitle => 'Explore all available categories';

  @override
  String get basicInformation => 'Basic Information';

  @override
  String get enterPropertyTitle => 'Enter property title';

  @override
  String get pleaseEnterPropertyTitle => 'Please enter property title';

  @override
  String get enterPropertyDescription => 'Enter property description';

  @override
  String get pleaseEnterDescription => 'Please enter description';

  @override
  String get pleaseEnterPrice => 'Please enter price';

  @override
  String get pleaseEnterValidPrice => 'Please enter a valid price';

  @override
  String get maxGuests => 'Max Guests';

  @override
  String get pleaseEnterMaxGuests => 'Please enter max guests';

  @override
  String get pleaseEnterValidNumber => 'Please enter valid number';

  @override
  String get pleaseEnterBedrooms => 'Please enter bedrooms';

  @override
  String get pleaseBathrooms => 'Please enter bathrooms';

  @override
  String get category => 'Category';

  @override
  String get pleaseSelectCategory => 'Please select category';

  @override
  String get facilities => 'Facilities';

  @override
  String get media => 'Media';

  @override
  String get mainImage => 'Main Image';

  @override
  String get video => 'Video';

  @override
  String get gallery => 'Gallery';

  @override
  String get addImages => 'Add Images';

  @override
  String get bookingRules => 'Booking Rules';

  @override
  String get enterBookingRules => 'Enter booking rules';

  @override
  String get cancellationRules => 'Cancellation Rules';

  @override
  String get enterCancellationRules => 'Enter cancellation rules';

  @override
  String get reviewSubmittedSuccessfully => 'Review submitted successfully';

  @override
  String get submitReview => 'Submit Review';

  @override
  String get loginRequiredForReservation =>
      'You need to login to make a reservation. Guest users can also make reservations with limited features.';

  @override
  String get loginRequiredForFavorites =>
      'You need to login to add items to your favorites list.';

  @override
  String get loginRequiredForReviews =>
      'You need to login to write reviews and share your experience.';

  @override
  String get guestModeInfo =>
      'As a guest, you can browse and make reservations, but some features like favorites and reviews require an account.';

  @override
  String get guestReservation => 'Guest Reservation';

  @override
  String get guestReservationMessage =>
      'You are currently browsing as a guest. You can proceed with the reservation, but creating an account will give you access to more features.';

  @override
  String get guestLimitations => 'Guest Limitations:';

  @override
  String get guestLimitationsDetails =>
      '• Cannot save favorites\n• Cannot write reviews\n• Limited reservation history\n• No profile management';

  @override
  String get loginForBetterExperience => 'Login for Better Experience';

  @override
  String get continueAsGuest => 'Continue as Guest';

  @override
  String get featureUnavailable => 'Feature Unavailable';

  @override
  String get featureRequiresLogin =>
      'This feature requires you to login. Please create an account or login to access this feature.';

  @override
  String get guest => 'Guest';

  @override
  String get retryConnection => 'Retry Connection';

  @override
  String get connectionError => 'Connection Error';

  @override
  String get serverError => 'Server Error';

  @override
  String get unknownError => 'Unknown Error';

  @override
  String get loadingData => 'Loading data...';

  @override
  String get refreshData => 'Refresh Data';

  @override
  String get noInternetConnection => 'No Internet Connection';

  @override
  String get checkInternetConnection =>
      'Please check your internet connection and try again';

  @override
  String get dataLoadFailed => 'Failed to load data';

  @override
  String get pullToRefresh => 'Pull to refresh';

  @override
  String get ofPreposition => 'of';

  @override
  String get tourismPermitNumber => 'Tourism Permit Number *';

  @override
  String get dataLoadError => 'Error loading data';

  @override
  String get noDataAvailable => 'No data available';

  @override
  String get reviewsCount => 'review';

  @override
  String get sarPerNight => 'SR/night';

  @override
  String get freeWifiArabic => 'Free WiFi';

  @override
  String get year => 'year';

  @override
  String get years => 'years';

  @override
  String get inHosting => 'hosting';

  @override
  String get newHost => 'New host';

  @override
  String get noDescriptionAvailable => 'No description available.';

  @override
  String get guestRating => 'Guest review';

  @override
  String get march2024 => 'March 2024';

  @override
  String get sampleReviewText =>
      'Great place to stay! Clean and comfortable and exactly as described. The host was very responsive and helpful.';

  @override
  String get showAllReviews => 'Show all';

  @override
  String get sar => 'SR';

  @override
  String get night => 'night';

  @override
  String get currencySymbol => 'SR';

  @override
  String get currencyCode => 'SAR';

  @override
  String priceWithCurrency(String price) {
    return '$price SR';
  }

  @override
  String get minimumWithdrawAmount => 'Minimum withdrawal: SR 50';

  @override
  String get smartEntry => 'Smart Entry';

  @override
  String get knowledge => 'Knowledge';

  @override
  String get previousTrips => 'Previous Trips';

  @override
  String get joinAsHost => 'Join as Host';

  @override
  String get joinAsHostSubtitle =>
      'It\'s easy to start hosting and earn extra income';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get requestHelp => 'Request Help';

  @override
  String get viewProfile => 'View Profile';

  @override
  String get referHost => 'Refer Host';

  @override
  String get legal => 'Legal';

  @override
  String get previousTrip => 'Previous Trip';

  @override
  String get yearsOnAirbnb => 'Years on Airbnb';

  @override
  String get cancellationPolicyNote =>
      'Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site\'s policy.';

  @override
  String get selectCancellationPolicy => 'Select Cancellation Policy';

  @override
  String get shortTermBookings => 'Short-term bookings (≤28 days)';

  @override
  String get longTermBookings => 'Long-term bookings (>28 days)';

  @override
  String get flexiblePolicy => 'Flexible';

  @override
  String get moderatePolicy => 'Moderate';

  @override
  String get strictPolicy => 'Strict';

  @override
  String get policyDescription => 'Policy Description';

  @override
  String get pleaseSelectPropertyType => 'Please select property type';

  @override
  String get pleaseSelectCancellationPolicy =>
      'Please select cancellation policy';

  @override
  String get selectLocation => 'Select Location';

  @override
  String get confirmLocation => 'Confirm Location';

  @override
  String get confirm => 'Confirm';

  @override
  String get propertyPreview => 'Property Preview';

  @override
  String get publishProperty => 'Publish Property';

  @override
  String get policies => 'Policies';

  @override
  String get listView => 'List View';

  @override
  String get gridView => 'Grid View';

  @override
  String get cancelSelection => 'Cancel Selection';

  @override
  String get selectMultiple => 'Select Multiple';

  @override
  String get addNewListing => 'Add New Listing';

  @override
  String get dashboardOverview => 'Dashboard Overview';

  @override
  String get totalRevenue => 'Total Revenue';

  @override
  String get averageRating => 'Average Rating';

  @override
  String get activeListings => 'Active Listings';

  @override
  String get occupancyRate => 'Occupancy Rate';

  @override
  String get conversionRate => 'Conversion Rate';

  @override
  String get allListings => 'All Listings';

  @override
  String get inactiveListings => 'Inactive Listings';

  @override
  String get drafts => 'Drafts';

  @override
  String get pendingReview => 'Pending Review';

  @override
  String get topPerforming => 'Top Performing';

  @override
  String get needsAttention => 'Needs Attention';

  @override
  String get searchListings => 'Search listings...';

  @override
  String get clearFilters => 'Clear Filters';

  @override
  String get noListingsYet => 'No Listings Yet';

  @override
  String get noListingsDescription =>
      'Start your hosting journey by creating your first property listing. Share your space with travelers and start earning!';

  @override
  String get createFirstListing => 'Create Your First Listing';

  @override
  String get hostingTips => 'Hosting Tips';

  @override
  String get tip1 => 'Add high-quality photos to attract more guests';

  @override
  String get tip2 => 'Write a detailed description of your property';

  @override
  String get tip3 => 'Set competitive pricing for your area';

  @override
  String get needHelp => 'Need help?';

  @override
  String get contactSupport => 'Contact Support';

  @override
  String get errorLoadingListings => 'Error Loading Listings';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get errorPersistsContact =>
      'If the error persists, please contact support';

  @override
  String get listingStatus => 'Listing Status';

  @override
  String get publishListing => 'Publish Listing';

  @override
  String get editListing => 'Edit Listing';

  @override
  String get deactivate => 'Deactivate';

  @override
  String get activate => 'Activate';

  @override
  String get editWhilePending => 'Edit While Pending';

  @override
  String get rejectionReason => 'Rejection Reason';

  @override
  String get pendingReservations => 'Pending Reservations';

  @override
  String get viewReservations => 'View Reservations';

  @override
  String get activeStatusDescription =>
      'Your listing is live and visible to guests';

  @override
  String get inactiveStatusDescription => 'Your listing is hidden from guests';

  @override
  String get draftStatusDescription =>
      'Your listing is saved but not published yet';

  @override
  String get pendingStatusDescription =>
      'Your listing is under review by our team';

  @override
  String get suspendedStatusDescription => 'Your listing has been suspended';

  @override
  String get unknownStatusDescription => 'Unknown status';

  @override
  String get publishListingConfirmation =>
      'Are you sure you want to publish this listing?';

  @override
  String get deactivateListingConfirmation =>
      'Are you sure you want to deactivate this listing?';

  @override
  String get deactivateListing => 'Deactivate Listing';

  @override
  String get changeListingStatus => 'Change Listing Status';

  @override
  String get currentStatus => 'Current Status';

  @override
  String get selectNewStatus => 'Select New Status';

  @override
  String get changeReason => 'Change Reason';

  @override
  String get enterChangeReason => 'Enter reason for status change...';

  @override
  String get changeStatus => 'Change Status';

  @override
  String get statusChangedSuccessfully => 'Status changed successfully';

  @override
  String get statusChangeError => 'Error changing status';

  @override
  String get bulkActions => 'Bulk Actions';

  @override
  String get listingsSelected => 'listings selected';

  @override
  String get currentStatusBreakdown => 'Current Status Breakdown';

  @override
  String get selectAction => 'Select Action';

  @override
  String get applyAction => 'Apply Action';

  @override
  String get activateAll => 'Activate All';

  @override
  String get deactivateAll => 'Deactivate All';

  @override
  String get convertToDraft => 'Convert to Draft';

  @override
  String get deleteAll => 'Delete All';

  @override
  String get activateAllDescription =>
      'Make all selected listings visible to guests';

  @override
  String get deactivateAllDescription =>
      'Hide all selected listings from guests';

  @override
  String get convertToDraftDescription =>
      'Convert all selected listings to draft status';

  @override
  String get deleteAllDescription => 'Permanently delete all selected listings';

  @override
  String get bulkActionCompleted => 'Bulk action completed successfully';

  @override
  String get bulkActionError => 'Error performing bulk action';

  @override
  String get analyticsOverview => 'Analytics Overview';

  @override
  String get last7Days => 'Last 7 Days';

  @override
  String get last30Days => 'Last 30 Days';

  @override
  String get last90Days => 'Last 90 Days';

  @override
  String get lastYear => 'Last Year';

  @override
  String get exportData => 'Export Data';

  @override
  String get performanceGrade => 'Performance Grade';

  @override
  String get overview => 'Overview';

  @override
  String get charts => 'Charts';

  @override
  String get insights => 'Insights';

  @override
  String get details => 'Details';

  @override
  String get responseTime => 'Response Time';

  @override
  String get responseRate => 'Response Rate';

  @override
  String get viewsTrend => 'Views Trend';

  @override
  String get dailyViews => 'Daily Views';

  @override
  String get dailyBookings => 'Daily Bookings';

  @override
  String get dailyRevenue => 'Daily Revenue';

  @override
  String get bookingMetrics => 'Booking Metrics';

  @override
  String get cancelledBookings => 'Cancelled Bookings';

  @override
  String get cancellationRate => 'Cancellation Rate';

  @override
  String get revenueMetrics => 'Revenue Metrics';

  @override
  String get netRevenue => 'Net Revenue';

  @override
  String get averageDailyRate => 'Average Daily Rate';

  @override
  String get engagementMetrics => 'Engagement Metrics';

  @override
  String get uniqueViews => 'Unique Views';

  @override
  String get favoriteCount => 'Favorites';

  @override
  String get shareCount => 'Shares';

  @override
  String get analytics => 'Analytics';

  @override
  String get advancedBulkActions => 'Advanced Bulk Actions';

  @override
  String get totalValue => 'Total Value';

  @override
  String get selectActionCategory => 'Select Action Category';

  @override
  String get statusActions => 'Status Actions';

  @override
  String get pricingActions => 'Pricing Actions';

  @override
  String get managementActions => 'Management Actions';

  @override
  String get executeAction => 'Execute Action';

  @override
  String get actionParameters => 'Action Parameters';

  @override
  String get percentage => 'Percentage';

  @override
  String get newPrice => 'New Price';

  @override
  String get discountPercentage => 'Discount Percentage';

  @override
  String get discountDuration => 'Discount Duration';

  @override
  String get publishAll => 'Publish All';

  @override
  String get increasePrices => 'Increase Prices';

  @override
  String get decreasePrices => 'Decrease Prices';

  @override
  String get setPrices => 'Set Prices';

  @override
  String get applyDiscount => 'Apply Discount';

  @override
  String get duplicateAll => 'Duplicate All';

  @override
  String get exportAll => 'Export All';

  @override
  String get archiveAll => 'Archive All';

  @override
  String get publishAllDescription => 'Publish all selected listings';

  @override
  String get increasePricesDescription => 'Increase prices by percentage';

  @override
  String get decreasePricesDescription => 'Decrease prices by percentage';

  @override
  String get setPricesDescription => 'Set fixed price for all listings';

  @override
  String get applyDiscountDescription => 'Apply temporary discount';

  @override
  String get duplicateAllDescription => 'Create copies of selected listings';

  @override
  String get exportAllDescription => 'Export listing data';

  @override
  String get archiveAllDescription => 'Archive selected listings';

  @override
  String get itemsSelected => 'items selected';

  @override
  String get clearSelection => 'Clear Selection';

  @override
  String get selectAll => 'Select All';

  @override
  String get moreActions => 'More Actions';

  @override
  String get activateSelected => 'Activate Selected';

  @override
  String get deactivateSelected => 'Deactivate Selected';

  @override
  String get deleteSelected => 'Delete Selected';

  @override
  String get activateSelectedConfirmation =>
      'Are you sure you want to activate the selected listings?';

  @override
  String get deactivateSelectedConfirmation =>
      'Are you sure you want to deactivate the selected listings?';

  @override
  String get deleteSelectedConfirmation =>
      'Are you sure you want to delete the selected listings? This action cannot be undone.';

  @override
  String get listingsWillBeAffected => 'listings will be affected';

  @override
  String get loadingPropertyData => 'Loading property data...';

  @override
  String get errorLoadingProperty => 'Error Loading Property';

  @override
  String get goBack => 'Go Back';

  @override
  String get propertyNotFound => 'Property Not Found';

  @override
  String get propertyNotFoundDescription =>
      'The property you\'re looking for doesn\'t exist or has been removed.';

  @override
  String get createNewProperty => 'Create New Property';

  @override
  String get propertyImages => 'Property Images';

  @override
  String get pleaseEnterTitle => 'Please enter property title';

  @override
  String get address => 'Address';

  @override
  String get saveAsDraft => 'Save as Draft';

  @override
  String get updateProperty => 'Update Property';

  @override
  String get propertyUpdatedSuccessfully => 'Property updated successfully';

  @override
  String get propertyTitleRequired => 'Property title is required';

  @override
  String get propertyTitleTooShort =>
      'Property title must be at least 3 characters';

  @override
  String get propertyDescriptionRequired => 'Property description is required';

  @override
  String get propertyDescriptionTooShort =>
      'Property description must be at least 10 characters';

  @override
  String get priceRequired => 'Price is required';

  @override
  String get priceInvalid => 'Please enter a valid price';

  @override
  String get priceMinimum => 'Minimum price is 50 SAR per night';

  @override
  String get categoryRequired => 'Please select a category';

  @override
  String get propertyTypeRequired => 'Please select a property type';

  @override
  String get cancellationPolicyRequired =>
      'Please select a cancellation policy';

  @override
  String get guestsRequired => 'Number of guests is required';

  @override
  String get guestsInvalid => 'Number of guests must be between 1 and 20';

  @override
  String get bedsRequired => 'Number of bedrooms is required';

  @override
  String get bedsInvalid => 'Number of bedrooms must be between 1 and 10';

  @override
  String get bathsRequired => 'Number of bathrooms is required';

  @override
  String get bathsInvalid => 'Number of bathrooms must be between 1 and 10';

  @override
  String get facilitiesRequired => 'Please select at least one facility';

  @override
  String get locationRequired => 'Please select a location';

  @override
  String get imagesRequired => 'Please add at least one image';

  @override
  String get imagesMinimum => 'Please add at least 3 images';

  @override
  String get categoryAndType => 'Category & Type';

  @override
  String get locationAndAddress => 'Location & Address';

  @override
  String get photosAndVideo => 'Photos & Video';

  @override
  String get reviewAndSubmit => 'Review & Submit';

  @override
  String get savingProperty => 'Saving property...';

  @override
  String get validationFailed => 'Please fix the errors and try again';

  @override
  String get basicInformationDesc => 'Tell us about your property';

  @override
  String get propertyTitleHint => 'Enter a catchy title for your property';

  @override
  String get propertyDescriptionHint => 'Describe your property in detail';

  @override
  String get priceHint => '100';

  @override
  String get priceGuidance =>
      'Tip: Research similar properties in your area to set competitive pricing';

  @override
  String get locationSelected => 'Location Selected';

  @override
  String get changeLocation => 'Change Location';

  @override
  String get propertyPhotos => 'Property Photos';

  @override
  String get propertyVideoOptional => 'Property Video (Optional)';

  @override
  String get addPhotos => 'Add Photos';

  @override
  String get addVideo => 'Add Video';

  @override
  String get changeVideo => 'Change Video';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get chooseFromGallery => 'Choose from Gallery';

  @override
  String get chooseMultiple => 'Choose Multiple';

  @override
  String get noPhotosAdded => 'No photos added yet';

  @override
  String get videoPreview => 'Video Preview';

  @override
  String get main => 'Main';

  @override
  String get previous => 'Previous';

  @override
  String get notProvided => 'Not provided';

  @override
  String get creatingProperty => 'Creating your property...';

  @override
  String get pleaseWaitProcessing =>
      'Please wait while we process your information';

  @override
  String get categoryTypeDescription =>
      'Choose the category and type that best describes your property';

  @override
  String get bookingRulesDescription =>
      'Set booking rules and provide tourism permit information';

  @override
  String get bookingRulesHint =>
      'Enter any specific rules for booking your property (optional)';

  @override
  String get tourismPermitNumberHint =>
      'Enter your tourism permit number (required)';

  @override
  String get tourismPermitDocument => 'Tourism Permit Document *';

  @override
  String get tourismPermitDocumentHint =>
      'Upload your tourism permit document (PDF, DOC, DOCX, JPG, PNG)';

  @override
  String get documentSelected => 'Document Selected';

  @override
  String get changeDocument => 'Change Document';

  @override
  String get uploadDocument => 'Upload Document';

  @override
  String get tourismPermitInfo =>
      'Tourism permit is required for all properties. This helps build trust with guests and ensures compliance with local regulations.';

  @override
  String get propertyDetailsDescription =>
      'Specify the details and amenities of your property';

  @override
  String get tourismPermitNumberRequired => 'Tourism permit number is required';

  @override
  String get tourismPermitNumberMinLength =>
      'Tourism permit number should be at least 5 characters';

  @override
  String get tourismPermitDocumentRequired =>
      'Tourism permit document is required';

  @override
  String get bookingRulesMinLength =>
      'Booking rules should be at least 10 characters if provided';

  @override
  String get reviewDetails => 'Review Details';

  @override
  String get title => 'Title';

  @override
  String get bookingRulesReview => 'Booking Rules';

  @override
  String get tourismPermitNumberReview => 'Tourism Permit Number';

  @override
  String get tourismPermitDocumentReview => 'Tourism Permit Document';

  @override
  String get photos => 'Photos';

  @override
  String get notSelected => 'Not selected';

  @override
  String get noneSelected => 'None selected';

  @override
  String get unknown => 'Unknown';

  @override
  String get selected => 'Selected';

  @override
  String get uploaded => 'Uploaded';

  @override
  String get notUploaded => 'Not uploaded';

  @override
  String get added => 'Added';

  @override
  String get notAdded => 'Not added';

  @override
  String get images => 'images';

  @override
  String get addPhoto => 'Add Photo';

  @override
  String get selectFacilities => 'Select Facilities';

  @override
  String get failedToLoadPropertyTypes => 'Failed to load property types';

  @override
  String get failedToLoadCancellationPolicies =>
      'Failed to load cancellation policies';

  @override
  String get loadingCategories => 'Loading categories...';

  @override
  String get loadingPropertyTypes => 'Loading property types...';

  @override
  String get loadingCancellationPolicies => 'Loading cancellation policies...';

  @override
  String get loadingFacilities => 'Loading facilities...';

  @override
  String get propertyTypeOption => 'Property type option';

  @override
  String get friends => 'Friends';

  @override
  String get requests => 'Requests';

  @override
  String get searchFriends => 'Search';

  @override
  String get searchFriendsHint => 'Search for friends by name or email';

  @override
  String get searchForFriends => 'Search for friends';

  @override
  String get searchForFriendsDescription =>
      'Type a name or email to search for new friends';

  @override
  String get noFriendsYet => 'No friends yet';

  @override
  String get noFriendsDescription =>
      'Start by adding new friends to connect with them';

  @override
  String get noPendingRequests => 'No pending requests';

  @override
  String get noPendingRequestsDescription =>
      'Incoming friend requests will appear here';

  @override
  String get noSearchResultsDescription => 'No users found with this name';

  @override
  String get addFriend => 'Add';

  @override
  String get acceptRequest => 'Accept';

  @override
  String get declineRequest => 'Decline';

  @override
  String get removeFriend => 'Remove friend';

  @override
  String get alreadyFriends => 'Friends';

  @override
  String get requestSent => 'Sent';

  @override
  String get host => 'Host';

  @override
  String get mutualFriends => 'mutual friends';

  @override
  String get friendRequestSentSuccess => 'Friend request sent successfully';

  @override
  String get friendRequestSentError => 'Error sending friend request';

  @override
  String get friendRequestAcceptedSuccess =>
      'Friend request accepted successfully';

  @override
  String get friendRequestAcceptedError => 'Error accepting friend request';

  @override
  String get friendRequestDeclinedSuccess =>
      'Friend request declined successfully';

  @override
  String get friendRequestDeclinedError => 'Error declining friend request';

  @override
  String get friendRemovedSuccess => 'Friend removed successfully';

  @override
  String get friendRemovedError => 'Error removing friend';

  @override
  String get searchError => 'Search error';

  @override
  String get loadingFriends => 'Loading friends...';

  @override
  String get loadingRequests => 'Loading requests...';

  @override
  String get refreshFriends => 'Refresh friends list';

  @override
  String get goToPendingRequests =>
      'Please go to the requests tab to accept the friend request';

  @override
  String get messageFeatureInDevelopment =>
      'Messaging feature is under development';

  @override
  String get failedToLoadFriends => 'Failed to load friends';

  @override
  String get failedToLoadRequests => 'Failed to load requests';

  @override
  String get acquaintances => 'Acquaintances';

  @override
  String get sendMessage => 'Send message';

  @override
  String get friendRequestTime => 'Friend request';

  @override
  String get retryButton => 'Retry';

  @override
  String get minutesAgo => 'minutes ago';

  @override
  String get hoursAgo => 'hours ago';

  @override
  String get daysAgo => 'days ago';

  @override
  String get ago => 'ago';

  @override
  String get hostModeActivated => 'Host mode activated successfully';

  @override
  String get hostModeDeactivated => 'Host mode deactivated successfully';

  @override
  String get earlyAccessFeatures => 'Early Access Features';

  @override
  String get newLabel => 'New';

  @override
  String get guestUser => 'Guest User';

  @override
  String get loginForFullExperience => 'Login for full experience';

  @override
  String get jeddahSaudiArabia => 'Jeddah, Saudi Arabia';

  @override
  String get loginRequired => 'Login Required';

  @override
  String get loginRequiredMessage => 'You must login to access this feature';

  @override
  String get switchToTravel => 'Switch to Travel';

  @override
  String get hosting => 'Hosting';

  @override
  String get editProfileTitle => 'Edit Profile';

  @override
  String get saveChangesTooltip => 'Save Changes';

  @override
  String get unexpectedError => 'An unexpected error occurred';

  @override
  String get pleaseSelectBirthdate => 'Please select birthdate';

  @override
  String get validationError => 'Validation error';

  @override
  String get accountSettingsTitle => 'Account Settings';

  @override
  String get loginRequiredForSettings =>
      'You must login to access account settings';

  @override
  String get accountInfo => 'Account Information';

  @override
  String get editProfileSubtitle =>
      'Update name, photo and personal information';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get phoneNumberLabel => 'Phone Number';

  @override
  String get security => 'Security';

  @override
  String get changePassword => 'Change Password';

  @override
  String get changePasswordSubtitle => 'Update your password';

  @override
  String get twoFactorAuth => 'Two-Factor Authentication';

  @override
  String get twoFactorAuthSubtitle => 'Additional security for your account';

  @override
  String get connectedDevices => 'Connected Devices';

  @override
  String get connectedDevicesSubtitle =>
      'Manage devices you\'re logged in from';

  @override
  String get preferences => 'Preferences';

  @override
  String get notificationsSubtitle => 'Manage notification settings';

  @override
  String get locationSubtitle => 'Privacy and location settings';

  @override
  String get dangerZone => 'Danger Zone';

  @override
  String get deleteAccountSubtitle =>
      'Permanently delete your account - cannot be undone';

  @override
  String get deleteAccountTitle => 'Delete Account';

  @override
  String get deleteAccountConfirmMessage =>
      'Are you sure you want to delete your account? This action cannot be undone and all your data will be lost.';

  @override
  String get delete => 'Delete';

  @override
  String get supportCenter => 'Support Center';

  @override
  String get errorOccurred => 'Error';

  @override
  String get frequentlyAskedQuestions => 'Frequently Asked Questions';

  @override
  String get supportTickets => 'Support Tickets';

  @override
  String get newTicket => 'New Ticket';

  @override
  String get noFaqsAvailable => 'No FAQs available';

  @override
  String get noSupportTickets => 'No support tickets';

  @override
  String get ticketCreatedSuccessfully => 'Ticket created successfully';

  @override
  String get createNewSupportTicket => 'Create New Support Ticket';

  @override
  String get subject => 'Subject';

  @override
  String get pleaseEnterSubject => 'Please enter subject';

  @override
  String get descriptionLabel => 'Description';

  @override
  String get priority => 'Priority';

  @override
  String get low => 'Low';

  @override
  String get medium => 'Medium';

  @override
  String get high => 'High';

  @override
  String get urgent => 'Urgent';

  @override
  String get submitTicket => 'Submit Ticket';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get quickHelp => 'Quick Help';

  @override
  String get howToSearch => 'How to Search';

  @override
  String get makeBooking => 'Make a Booking';

  @override
  String get paymentAndBilling => 'Payment & Billing';

  @override
  String get frequentQuestions => 'Frequently Asked Questions';

  @override
  String get howToCancelBooking => 'How can I cancel a booking?';

  @override
  String get howToCancelBookingAnswer =>
      'You can cancel the booking by going to \"My Bookings\" and selecting the booking you want to cancel.';

  @override
  String get refundPolicy => 'What is the refund policy?';

  @override
  String get refundPolicyAnswer =>
      'Refund policy varies by property type and host policy. You can review the details on the booking page.';

  @override
  String get howToChangeBooking => 'How can I change booking information?';

  @override
  String get howToChangeBookingAnswer =>
      'You can modify some booking information by contacting the host or customer service.';

  @override
  String get howToBecomeHost => 'How do I become a host?';

  @override
  String get howToBecomeHostAnswer =>
      'You can switch to host mode from your profile and add your first property.';

  @override
  String get liveChat => 'Live Chat';

  @override
  String get available24_7 => 'Available 24/7';

  @override
  String get emailSupport => 'Email Support';

  @override
  String get phoneSupport => 'Phone Support';

  @override
  String get usefulResources => 'Useful Resources';

  @override
  String get userGuide => 'User Guide';

  @override
  String get tutorialVideos => 'Tutorial Videos';

  @override
  String get helpCenter => 'Help Center';

  @override
  String get viewProfileTitle => 'View Profile';

  @override
  String get loginRequiredToViewProfile => 'Login required to view profile';

  @override
  String get menu => 'Menu';

  @override
  String get switchToTravelMode => 'Switch to Travel';

  @override
  String get trips => 'Trips';

  @override
  String get aboutMe => 'About Me';

  @override
  String get noAboutMeAdded => 'No personal bio added yet.';

  @override
  String get languages => 'Languages';

  @override
  String get arabicEnglish => 'Arabic, English';

  @override
  String get locationLabel => 'Location';

  @override
  String get memberSince => 'Member since';

  @override
  String get january2023 => 'January 2023';

  @override
  String get verification => 'Verification';

  @override
  String get emailVerification => 'Email';

  @override
  String get phoneVerification => 'Phone Number';

  @override
  String get identityVerification => 'Identity';

  @override
  String get manageNotificationSettings => 'Manage notification settings';

  @override
  String get privacyLocationSettings => 'Privacy and location settings';

  @override
  String get faq => 'FAQ';

  @override
  String get errorMessage => 'Error';

  @override
  String get cancelBookingAnswer =>
      'You can cancel your booking by going to \"My Bookings\" and selecting the booking you want to cancel.';

  @override
  String get changeBookingInfo => 'How can I change booking information?';

  @override
  String get changeBookingAnswer =>
      'You can modify some booking information by contacting the host or customer service.';

  @override
  String get becomeHost => 'How do I become a host?';

  @override
  String get becomeHostAnswer =>
      'You can switch to host mode from your profile and add your first property.';

  @override
  String get available247 => 'Available 24/7';

  @override
  String get jeddahSaudi => 'Jeddah, Saudi Arabia';

  @override
  String get noBioAdded => 'No bio added yet.';

  @override
  String get rating0 => '0 rating';

  @override
  String get accountInfoTitle => 'Account Information';

  @override
  String get personalInfoTab => 'Personal Information';

  @override
  String get securityTab => 'Security';

  @override
  String get statisticsTab => 'Statistics';

  @override
  String get noInfoAvailable => 'No information available';

  @override
  String get noSecuritySettingsAvailable => 'No security settings available';

  @override
  String get noStatisticsAvailable => 'No statistics available';

  @override
  String get editPersonalInfo => 'Edit Personal Information';

  @override
  String get exportAccountData => 'Export Account Data';

  @override
  String get activeSessions => 'Active Sessions';

  @override
  String get recentActivities => 'Recent Activities';

  @override
  String get accountStatistics => 'Account Statistics';

  @override
  String get memberSinceLabel => 'Member Since';

  @override
  String get lastLogin => 'Last Login';

  @override
  String get totalBookingsLabel => 'Total Bookings';

  @override
  String get totalReviewsLabel => 'Total Reviews';

  @override
  String get verifiedAccount => 'Verified Account';

  @override
  String get unverifiedAccount => 'Unverified Account';

  @override
  String get currentSession => 'Current';

  @override
  String get dataExportedSuccessfully => 'Data Exported Successfully';

  @override
  String get dataExportSuccessMessage =>
      'Your data has been exported successfully';

  @override
  String get bookingsCount => 'booking';

  @override
  String get completePersonalInfo => 'Complete personal information';

  @override
  String get cannotLoadUserData => 'Cannot load user data. Please try again.';

  @override
  String get changePasswordTitle => 'Change Password';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get enterCurrentPassword => 'Please enter current password';

  @override
  String get enterNewPassword => 'Please enter new password';

  @override
  String get passwordMinLength => 'Password must be at least 8 characters';

  @override
  String get confirmNewPasswordField => 'Please confirm new password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get changePasswordButton => 'Change Password';

  @override
  String get passwordChangedSuccessfully => 'Password changed successfully';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get hostingResources => 'Hosting Resources';

  @override
  String get findCoHost => 'Find Co-Host';

  @override
  String get createNewListing => 'Create New Listing';

  @override
  String get privacySettingsTitle => 'Privacy Settings';

  @override
  String get profilePrivacy => 'Profile Privacy';

  @override
  String get showProfile => 'Show Profile';

  @override
  String get showProfileSubtitle => 'Allow others to see your profile';

  @override
  String get showEmail => 'Show Email';

  @override
  String get showEmailSubtitle => 'Display your email in profile';

  @override
  String get showPhone => 'Show Phone Number';

  @override
  String get showPhoneSubtitle => 'Display your phone number in profile';

  @override
  String get dataAndLocation => 'Data & Location';

  @override
  String get shareLocation => 'Share Location';

  @override
  String get shareLocationSubtitle => 'Allow app to access your location';

  @override
  String get analyticsDataCollection => 'Analytics Data Collection';

  @override
  String get analyticsDataCollectionSubtitle =>
      'Help us improve the app with anonymous data';

  @override
  String get downloadMyData => 'Download My Data';

  @override
  String get downloadMyDataSubtitle => 'Get a copy of all your data';

  @override
  String get communication => 'Communication';

  @override
  String get allowMessages => 'Allow Messages';

  @override
  String get allowMessagesSubtitle =>
      'Allow hosts and guests to send you messages';

  @override
  String get pushNotificationsSubtitle =>
      'Receive instant notifications for messages and bookings';

  @override
  String get marketingEmails => 'Marketing Emails';

  @override
  String get marketingEmailsSubtitle =>
      'Receive email messages about offers and news';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get clearSearchHistory => 'Clear Search History';

  @override
  String get clearSearchHistorySubtitle => 'Delete all previous searches';

  @override
  String get clearCache => 'Clear Cache';

  @override
  String get clearCacheSubtitle => 'Delete temporary files and saved images';

  @override
  String get blockedUsers => 'Blocked Users';

  @override
  String get blockedUsersSubtitle => 'Manage blocked users list';

  @override
  String get downloadData => 'Download Data';

  @override
  String get downloadDataMessage =>
      'A copy of all your data will be sent to your email within 24 hours.';

  @override
  String get downloadDataSuccess => 'Data download request successful';

  @override
  String get clearSearchHistoryTitle => 'Clear Search History';

  @override
  String get clearSearchHistoryMessage =>
      'Do you want to delete all previous searches?';

  @override
  String get searchHistoryCleared => 'Search history cleared';

  @override
  String get clearCacheTitle => 'Clear Cache';

  @override
  String get clearCacheMessage => 'Do you want to delete all temporary files?';

  @override
  String get cacheCleared => 'Cache cleared';

  @override
  String get clear => 'Clear';

  @override
  String get logoutSuccessful => 'Logout Successful';

  @override
  String get chooseWhatToDoNow => 'Choose what to do now';
}
