import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  bool _profileVisibility = true;
  bool _showEmail = false;
  bool _showPhone = false;
  bool _allowMessages = true;
  bool _shareLocation = true;
  bool _dataCollection = true;
  bool _marketingEmails = false;
  bool _pushNotifications = true;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      title: s.privacySettingsTitle,
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Privacy Section
            _ProfilePrivacySection(),
            const SizedBox(height: 16),
            
            // Data & Location Section
            _DataLocationSection(),
            const SizedBox(height: 16),
            
            // Communication Section
            _CommunicationSection(),
            const SizedBox(height: 16),
            
            // Data Management Section
            _DataManagementSection(),
          ],
        ),
      ),
    );
  }

  Widget _ProfilePrivacySection() {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.profilePrivacy,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),

          _PrivacyToggleItem(
            title: s.showProfile,
            subtitle: s.showProfileSubtitle,
            value: _profileVisibility,
            onChanged: (value) {
              setState(() {
                _profileVisibility = value;
              });
            },
          ),

          _PrivacyToggleItem(
            title: s.showEmail,
            subtitle: s.showEmailSubtitle,
            value: _showEmail,
            onChanged: (value) {
              setState(() {
                _showEmail = value;
              });
            },
          ),

          _PrivacyToggleItem(
            title: s.showPhone,
            subtitle: s.showPhoneSubtitle,
            value: _showPhone,
            onChanged: (value) {
              setState(() {
                _showPhone = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _DataLocationSection() {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.dataAndLocation,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),

          _PrivacyToggleItem(
            title: s.shareLocation,
            subtitle: s.shareLocationSubtitle,
            value: _shareLocation,
            onChanged: (value) {
              setState(() {
                _shareLocation = value;
              });
            },
          ),

          _PrivacyToggleItem(
            title: s.analyticsDataCollection,
            subtitle: s.analyticsDataCollectionSubtitle,
            value: _dataCollection,
            onChanged: (value) {
              setState(() {
                _dataCollection = value;
              });
            },
          ),

          _PrivacyActionItem(
            icon: Icons.download_outlined,
            title: s.downloadMyData,
            subtitle: s.downloadMyDataSubtitle,
            onTap: () {
              _showDataDownloadDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _CommunicationSection() {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.communication,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),

          _PrivacyToggleItem(
            title: s.allowMessages,
            subtitle: s.allowMessagesSubtitle,
            value: _allowMessages,
            onChanged: (value) {
              setState(() {
                _allowMessages = value;
              });
            },
          ),

          _PrivacyToggleItem(
            title: s.pushNotifications,
            subtitle: s.pushNotificationsSubtitle,
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
            },
          ),

          _PrivacyToggleItem(
            title: s.marketingEmails,
            subtitle: s.marketingEmailsSubtitle,
            value: _marketingEmails,
            onChanged: (value) {
              setState(() {
                _marketingEmails = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _DataManagementSection() {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.dataManagement,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),

          _PrivacyActionItem(
            icon: Icons.history_outlined,
            title: s.clearSearchHistory,
            subtitle: s.clearSearchHistorySubtitle,
            onTap: () {
              _showClearSearchHistoryDialog();
            },
          ),

          _PrivacyActionItem(
            icon: Icons.cached_outlined,
            title: s.clearCache,
            subtitle: s.clearCacheSubtitle,
            onTap: () {
              _showClearCacheDialog();
            },
          ),

          _PrivacyActionItem(
            icon: Icons.block_outlined,
            title: s.blockedUsers,
            subtitle: s.blockedUsersSubtitle,
            onTap: () {
              // Navigate to blocked users
            },
          ),
        ],
      ),
    );
  }

  Widget _PrivacyToggleItem({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font16Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: context.accentColor,
          ),
        ],
      ),
    );
  }

  Widget _PrivacyActionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: context.secondaryTextColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.font16Regular.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: context.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showDataDownloadDialog() {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(s.downloadData),
        content: Text(s.downloadDataMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(s.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement data download request
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(s.downloadDataSuccess),
                ),
              );
            },
            child: Text(s.confirm),
          ),
        ],
      ),
    );
  }

  void _showClearSearchHistoryDialog() {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(s.clearSearchHistoryTitle),
        content: Text(s.clearSearchHistoryMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(s.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement clear search history
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(s.searchHistoryCleared),
                ),
              );
            },
            child: Text(s.clear),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog() {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(s.clearCacheTitle),
        content: Text(s.clearCacheMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(s.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement clear cache
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(s.cacheCleared),
                ),
              );
            },
            child: Text(s.clear),
          ),
        ],
      ),
    );
  }
}
